# 职业病档案抽取接口测试文档

## 接口概述

根据身份证号抽取劳动者职业病诊断档案和鉴定档案的两个新接口已经实现完成。

## 新增接口

### 1. 抽取职业病诊断档案

**接口地址**: `GET /dak/extractDiagnosticArchive`

**请求参数**:
- `idNumber` (string): 身份证号

**功能描述**:
1. 根据身份证号从users表获取劳动者基本信息（姓名、性别等）
2. 根据身份证号获取该劳动者的所有诊断记录，按诊断日期升序排序
3. 汇总数据并保存到DiagnosticArchives表，使用shortid.generate()生成_id

**返回数据结构**:
```json
{
  "_id": "shortid生成的字符串",
  "idNumber": "身份证号",
  "name": "姓名",
  "age": 年龄,
  "gender": "性别",
  "diagnosticRecord": [
    // 所有诊断记录，按诊断日期排序
  ],
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

### 2. 抽取职业病鉴定档案

**接口地址**: `GET /dak/extractDeterminationArchive`

**请求参数**:
- `idNumber` (string): 身份证号

**功能描述**:
1. 根据身份证号从users表获取劳动者基本信息（姓名、性别等）
2. 根据身份证号获取该劳动者的所有鉴定记录，按鉴定日期升序排序
3. 汇总数据并保存到DeterminationArchives表，使用shortid.generate()生成_id

**返回数据结构**:
```json
{
  "_id": "shortid生成的字符串",
  "idNumber": "身份证号",
  "name": "姓名",
  "age": 年龄,
  "gender": "性别",
  "determinationRecord": [
    // 所有鉴定记录，按鉴定日期排序
  ],
  "createdAt": "创建时间",
  "updatedAt": "更新时间"
}
```

## 实现特点

1. **完整历史记录**: 获取劳动者的所有历史诊断/鉴定记录，而不是仅最新记录
2. **时间排序**: 按诊断日期/鉴定日期升序排序，便于查看时间线
3. **数据完整性**: 包含劳动者基本信息和完整的记录列表
4. **ID生成**: 使用shortid.generate()生成_id，符合项目规范
5. **错误处理**: 完善的错误处理和日志记录

## 测试建议

1. 准备测试数据：确保数据库中有用户记录和对应的诊断/鉴定记录
2. 测试正常流程：使用有效身份证号调用接口
3. 测试异常情况：
   - 不存在的身份证号
   - 存在用户但无诊断/鉴定记录的情况
4. 验证数据完整性：检查返回的档案是否包含所有历史记录
5. 验证排序：确认记录按时间正确排序

## 注意事项

- 接口会在数据库中创建新的档案记录，请注意数据管理
- 如果用户不存在或无相关记录，接口会抛出错误
- 年龄计算基于身份证号中的出生日期
