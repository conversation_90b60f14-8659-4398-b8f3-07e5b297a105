import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { Dak<PERSON>ontroller } from './dak.controller';
import { DakService } from './dak.service';
import { DiagnosticRecord } from './entities/diagnosticRecord.entity';
import { DeterminationRecord } from './entities/determinationRecord.entity';
import { Users } from '../sxccduijie/entities/users.entity';
import { Employees } from '../sxccduijie/entities/employees.entity';
import { HealthCheckRegister } from './entities/HealthCheckRegister.entity';
import { HealthSurvArchives } from './entities/HealthSurvArchives.entity';
import { EmployeeBasicInfo } from './entities/employeeBasicInfo.entity';
import { OccupationalHistory } from './entities/OccupationalHistory.entity';
import { Workspace } from './entities/Workspace.entity';
import { CheckAssessment } from './entities/CheckAssessment.entity';
import { OccupationalExposureLimits } from './entities/occupationalExposureLimits.entity';
import { DiagnosticArchives } from './entities/DiagnosticArchives.entity';
import { DeterminationArchives } from './entities/DeterminationArchives.entity';


@Module({
  imports: [
    TypeOrmModule.forFeature(
      [
        DiagnosticRecord,
        DeterminationRecord,
        Users,
        HealthCheckRegister,
        HealthSurvArchives,
        Employees,
        EmployeeBasicInfo,
        OccupationalHistory,
        Workspace,
        CheckAssessment,
        OccupationalExposureLimits,
        DiagnosticArchives,
        DeterminationArchives,
      ],
      'mongodbConnection',
    ),
  ],
  controllers: [DakController],
  providers: [DakService],
  exports: [DakService],
})
export class DakModule { }
