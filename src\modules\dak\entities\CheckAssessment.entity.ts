import { Entity, Column, CreateDateColumn, UpdateDateColumn, BeforeInsert,ObjectIdColumn } from 'typeorm';
import * as shortid from 'shortid';

// 检测结果接口
interface CheckResult {
  _id: string;
  date: Date; // 检测日期
  MAC: string;
  TWA: string;
  STEL: string;
  PE: string; // 峰接触浓度
  excursionLimits: string; // 超限倍数
  checkResultItem: string; // 判定结果 现状评价
}

// 化学因素表单数据接口
interface ChemistryFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  level: string; // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
  percent: number; // 危害因素浓度百分比
  checkProject: string; // 检测项目(危害因素)
  checkResults: CheckResult[];
  touchLimitMAC: string; // MAC接触限值
  touchLimitTWA: string; // twa接触限值
  touchLimitSTEL: string; // STEL接触限值
  touchLimitPE: string; // PE接触限值
  checkResult: string; // 判定结果
}

// 粉尘因素表单数据接口
interface DustFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  checkProject: string; // 检测项目
  checkResults: CheckResult[];
  PELimit: string; // PE接触限值
  TWALimit: string; // TWA接触限值
  AllDustPercent: number; // 总尘百分比
  respirableDustPercent: number; // 呼尘百分比
  checkResult: string; // 判定结果
  level: string; // 危害因素浓度等级 0:<=1%OEL; I:>1% <=10%OEL; II:>10% <=50%OEL; III:>50% <100%OEL; IV:>100%OEL
  percent: number; // 危害因素浓度百分比
}

// 噪声因素表单数据接口
interface NoiseFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  touchTime: string; // 接触时间
  equalLevel: string; // 8/40h等效声级检测数值[dB(A)]
  checkData: string; // 检测值[dB(A)]
  touchLimit: string; // 职业接触限值
  checkResult: string; // 判定结果
}

// 手传振动因素表单数据接口
interface HandBorneVibrationFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 点位
  checkAddress: string; // 监测点位置
  dayTouchTime: string; // 日接触时间
  touchLimit: string; // 接触限值
  ahw: string; // 频率计权振动加速度测量值ahw（m/s2）
  fourHoursAccelerated: string; // 4h等能量频率计权振动加速度值（m/s2）
  checkResult: string; // 判定结果
}

// 高温因素表单数据接口
interface HeatFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  averageValue: string; // WBGT平均值
  touchTime: string; // 接触时间
  labourIntensity: string; // 体力劳动强度
  touchLimit: string; // 职业接触限值（℃）
  checkResult: string; // 评价结论
}

// 高频电磁场因素表单数据接口
interface HighFrequencyEleFormData {
  _id: string;
  mill: string; // 厂房
  workType: string; // 工种
  workspace: string; // 车间
  station: string; // 岗位
  checkAddress: string; // 监测点位置
  harmFactors: string; // 职业病危害因素名称
  radiationHZ: string; // 辐射频率（MHz）
  electricIntensity: string; // 电场强度（V/m）
  electricIntensityData: string; // 电场强度测量值（V/m）
  magneticIntensity: string; // 磁场强度（A/m）
  magneticIntensityData: string; // 磁场强度测量值（A/m）
  checkResult: string; // 判定结果
}

// 激光辐射因素表单数据接口
interface LaserFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddress: string; // 监测点位置
  touchLimit: string; // 接触限值
  testAverage: string; // 平均检测值（W/cm2）
  irradiance: string; // 辐射度
  checkResult: string; // 判定结果
}

// 微波辐射因素表单数据接口
interface MicrowaveFormData {
  _id: string;
  mill: string; // 厂房
  workType: string; // 工种
  workspace: string; // 车间
  station: string; // 岗位
  checkAddress: string; // 监测点位置
  shortTimeContactPowerDensity: string; // 短时间接触功率密度最大值（mW/cm2）
  shortTimeLimit: string; // 短时间接触功率密度接触限值（mW/cm2）
  average: string; // 平均值（μW/cm2）
  averagePowerDensityLimit: string; // 8h平均功率密度接触限值（μW/cm2）
  checkResult: string; // 判定结果
}

// 工频电场因素表单数据接口
interface PowerFrequencyElectricFormData {
  _id: string;
  mill: string;
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  electricIntensity: string; // 电场强度测量值（kV/m）
  electricIntensityLimit: string; // 电场强度职业接触限值（kV/m）
  checkResult: string; // 判定结果
}

// 游离二氧化硅因素表单数据接口
interface SiO2FormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 点位
  checkAddress: string; // 监测点位置
  checkProject: string; // 检测项目
  checkResult: string; // 判定结果
  checkType: string; // 检测结果类型 >=10%矽尘 其余是呼尘
}

// 超高频辐射因素表单数据接口
interface UltraHighRadiationFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddressDetail: string; // 具体监测点位置
  electricAverage: string; // 脉冲波电场强度平均值（V/m）
  eightHoursTouchLimit: string; // 8h职业接触限值（V/m）
  checkResult: string; // 判定结果
}

// 紫外辐射因素表单数据接口
interface UltravioletFormData {
  _id: string;
  mill: string;
  workspace: string;
  workType: string; // 工种
  station: string;
  checkAddress: string; // 监测点位置
  irradiance: string; // 有效辐照度（μW/cm2）
  eightHoursTouchLimit: string; // 8h职业接触限值（μW/cm2）
  checkResult: string; // 判定结果
}

// 电离辐射-含源装置因素表单数据接口
interface IonizatioSourceFormData {
  _id: string;
  checkProject: string; // 检测项目
  device: string; // 装置名称
  sourceNum: string; // 放射源编号
  model: string; // 编号 型号
  markNum: string; // 标号
  nuclide: string; // 核素
  installAddress: string; // 安装位置
  curActivity: string; // 当前活度
  deliveryActivity: string; // 出厂活度
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string;
  station: string; // 岗位
  checkAddress: string; // 检测点位置
  checkResult: string; // 判定结果
  checkResultValue: string; // 检测结果
}

// 电离辐射-射线装置因素表单数据接口
interface IonizatioRadialFormData {
  _id: string;
  checkProject: string; // 检测项目
  device: string; // 装置名称
  model: string; // 编号 型号
  ratedCapacity: string; // 额定容器
  condition: string; // 检测条件
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string;
  station: string; // 岗位
  checkAddress: string; // 检测点位置
  checkResult: string; // 判定结果
  checkResultValue: string; // 检测结果
}

// 生物因素表单数据接口
interface BiologicalFormData {
  _id: string;
  mill: string; // 厂房
  workspace: string; // 车间
  workType: string; // 工种
  station: string; // 岗位
  checkAddress: string; // 监测点位置
  checkProject: string; // 检测项目
  MAC: string;
  TWA: string;
  STEL: string;
  checkResult: string; // 判定结果
}

/**
 * 检测结果实体
 */
@Entity('checkAssessment')
export class CheckAssessment {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '类别 1.危害检测结果;2.现状评价 3.控制效果评价', nullable: true })
  category?: string;

  @Column({ comment: '年份', nullable: true })
  year?: Date;

  @Column({ comment: '数据来源，enterprise、service、super、operate', default: 'enterprise' })
  source: string;

  @Column({ comment: '企业id', nullable: true })
  EnterpriseID?: string;

  @Column({ comment: '检测项目id', nullable: true })
  jobHealthId?: string;

  @Column({ comment: '全流程id', nullable: true })
  jcqlcProjectId?: string;

  // 化学因素
  @Column({ comment: '化学物质检测结果', nullable: true })
  chemistryFactors?: {
    name: string;
    value: string;
    formData: ChemistryFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 粉尘因素
  @Column({ comment: '粉尘检测结果', nullable: true })
  dustFactors?: {
    name: string;
    value: string;
    formData: DustFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 噪声因素
  @Column({ comment: '噪声检测结果', nullable: true })
  noiseFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: NoiseFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 手传振动因素
  @Column({ comment: '手传振动检测结果', nullable: true })
  handBorneVibrationFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: HandBorneVibrationFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 高温因素
  @Column({ comment: '高温检测结果', nullable: true })
  heatFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: HeatFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 高频电磁场检测结果
  @Column({ comment: '高频电磁场检测结果', nullable: true })
  highFrequencyEleFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: HighFrequencyEleFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 激光辐射
  @Column({ comment: '激光辐射检测结果', nullable: true })
  laserFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: LaserFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 微波辐射
  @Column({ comment: '微波辐射检测结果', nullable: true })
  microwaveFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: MicrowaveFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 工频电场
  @Column({ comment: '工频电场检测结果', nullable: true })
  powerFrequencyElectric?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: PowerFrequencyElectricFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 游离二氧化硅
  @Column({ comment: '游离二氧化硅检测结果', nullable: true })
  SiO2Factors?: {
    name: string;
    value: string;
    formData: SiO2FormData[];
    // 不统计点数
  };

  // 超高频辐射
  @Column({ comment: '超高频辐射检测结果', nullable: true })
  ultraHighRadiationFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: UltraHighRadiationFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 紫外辐射
  @Column({ comment: '紫外辐射检测结果', nullable: true })
  ultravioletFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: UltravioletFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 电离辐射-含源装置
  @Column({ comment: '电离辐射-含源装置检测结果', nullable: true })
  ionizatioSourceFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: IonizatioSourceFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 电离辐射-射线装置
  @Column({ comment: '电离辐射-射线装置检测结果', nullable: true })
  ionizatioRadialFactors?: {
    factorType: string; // 危害因素类型
    name: string;
    value: string;
    formData: IonizatioRadialFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  // 生物因素
  @Column({ comment: '生物因素检测结果', nullable: true })
  biologicalFactors?: {
    name: string;
    value: string;
    formData: BiologicalFormData[];
    exceedCount: number; // 超标点位数
    allCount: number; // 总点位数
  };

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaults() {
    if (!this._id) {
      this._id = shortid.generate();
    }
    const now = new Date();
    this.createdAt = now;
    this.updatedAt = now;
  }
}
