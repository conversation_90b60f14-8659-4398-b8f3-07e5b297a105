import { En<PERSON>ty, Column, ObjectIdC<PERSON>umn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';

// 既往史接口
interface PastHistory {
    _id: string;
    diseaseName: string;
    diagnosisDate: string;
    institutionName: string;
    treatmentProcess: string;
    outcomeCode: string;
}

// 工作史接口
interface WorkHistory {
    _id: string;
    entryTime: string;
    leaveTime: string;
    workUnit: string;
    workshop: string;
    station: string;
    workType: string;
}

// 危害因素接口
interface HarmFactor {
    _id: string;
    name: string;
    code: string;
    category: string;
}

// 职业危害接触史接口
interface ExposureHistory {
    _id: string;
    entryTime: string;
    leaveTime: string;
    workUnit: string;
    workshop: string;
    station: string;
    workType: string;
    harmFactors: HarmFactor[];
}

@Entity('healthSurvArchives')
export class HealthSurvArchives {
    @ObjectIdColumn()
    _id: string;

    @Column({ comment: '身份证号' })
    idNumber: string;

    @Column({ comment: '姓名' })
    name: string;

    @Column({ comment: '年龄' })
    age: number;

    @Column({ comment: '性别' })
    gender: string;

    @Column({ comment: '联系电话' })
    phoneNum: string;

    @Column({ comment: '籍贯' })
    nativePlace: string;

    @Column({ comment: '嗜好' })
    hobby: string;

    @Column({ comment: '婚姻状况' })
    marriage: string;

    @Column({ comment: '企业ID' })
    EnterpriseID?: string;

    @Column({ comment: '企业名称' })
    cname?: string;

    @Column({ comment: '企业代码' })
    cCode?: string;

    @Column({ comment: '员工ID' })
    employeeId: string;

    @Column({ comment: '用户ID' })
    userId: string;

    @Column({ comment: '既往史' })
    pastHistory: PastHistory[] = [];

    @Column({ comment: '工作史' })
    workHistory: WorkHistory[] = [];

    @Column({ comment: '职业危害接触史' })
    exposureHistory: ExposureHistory[] = [];

    @Column({ comment: '作业场所危害因素检测结果' })
    workspaceHarmResult: any[] = [];

    @Column({ comment: '健康检查结果' })
    healthCheckResult: any[] = [];

    @CreateDateColumn({ comment: '创建时间' })
    createdAt: Date;

    @UpdateDateColumn({ comment: '更新时间' })
    updatedAt: Date;

    @BeforeInsert()
    setDefaults() {
        if (!this._id) {
            this._id = shortid.generate();
        }
        const now = new Date();
        this.createdAt = now;
        this.updatedAt = now;
    }
}
