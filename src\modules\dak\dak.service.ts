import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { MongoRepository } from 'typeorm';
import { DiagnosticRecord } from './entities/diagnosticRecord.entity';
import { DeterminationRecord } from './entities/determinationRecord.entity';
import { CreateDiagnosticRecordDto } from './dto/create-diagnostic-record.dto';
import { CreateDeterminationRecordDto } from './dto/create-determination-record.dto';
import { Users } from '../sxccduijie/entities/users.entity';
import { Employees } from '../sxccduijie/entities/employees.entity';
import { EmployeeBasicInfo } from './entities/employeeBasicInfo.entity';
import { HealthCheckRegister } from './entities/HealthCheckRegister.entity';
import { HealthSurvArchives } from './entities/HealthSurvArchives.entity';
import { OccupationalHistory } from './entities/OccupationalHistory.entity';
import { Workspace } from './entities/Workspace.entity';
import { CheckAssessment } from './entities/CheckAssessment.entity';
import { OccupationalExposureLimits } from './entities/occupationalExposureLimits.entity';
import { DiagnosticArchives } from './entities/DiagnosticArchives.entity';
import { DeterminationArchives } from './entities/DeterminationArchives.entity';
import * as moment from 'moment';
import * as shortid from 'shortid';

@Injectable()
export class DakService {
  private readonly logger = new Logger(DakService.name);

  constructor(
    @InjectRepository(DiagnosticRecord, 'mongodbConnection')
    private diagnosticRecordRepository: MongoRepository<DiagnosticRecord>,
    @InjectRepository(DeterminationRecord, 'mongodbConnection')
    private determinationRecordRepository: MongoRepository<DeterminationRecord>,
    @InjectRepository(Users, 'mongodbConnection')
    private usersRepository: MongoRepository<Users>,
    @InjectRepository(Employees, 'mongodbConnection')
    private employeesRepository: MongoRepository<Employees>,
    @InjectRepository(HealthCheckRegister, 'mongodbConnection')
    private healthCheckRegisterRepository: MongoRepository<HealthCheckRegister>,
    @InjectRepository(HealthSurvArchives, 'mongodbConnection')
    private healthSurvArchivesRepository: MongoRepository<HealthSurvArchives>,
    @InjectRepository(EmployeeBasicInfo, 'mongodbConnection')
    private employeeBasicInfoRepository: MongoRepository<EmployeeBasicInfo>,
    @InjectRepository(OccupationalHistory, 'mongodbConnection')
    private occupationalHistoryRepository: MongoRepository<OccupationalHistory>,
    @InjectRepository(Workspace, 'mongodbConnection')
    private workspaceRepository: MongoRepository<Workspace>,
    @InjectRepository(CheckAssessment, 'mongodbConnection')
    private checkAssessmentRepository: MongoRepository<CheckAssessment>,
    @InjectRepository(OccupationalExposureLimits, 'mongodbConnection')
    private occupationalExposureLimitsRepository: MongoRepository<OccupationalExposureLimits>,
    @InjectRepository(DiagnosticArchives, 'mongodbConnection')
    private diagnosticArchivesRepository: MongoRepository<DiagnosticArchives>,
    @InjectRepository(DeterminationArchives, 'mongodbConnection')
    private determinationArchivesRepository: MongoRepository<DeterminationArchives>,
  ) { }

  /**
   * 创建或更新诊断记录
   * @param createDiagnosticRecordDto 诊断记录DTO
   * @returns 创建或更新的诊断记录
   */
  async createOrUpdateDiagnosticRecord(
    createDiagnosticRecordDto: CreateDiagnosticRecordDto,
  ): Promise<DiagnosticRecord> {
    try {
      // 查询是否存在相同身份证和诊断编号的记录
      const existingRecord = await this.diagnosticRecordRepository.findOne({
        where: {
          idNumber: createDiagnosticRecordDto.idNumber,
          diagnosisNumber: createDiagnosticRecordDto.diagnosisNumber,
        },
      });

      let result: DiagnosticRecord;

      if (existingRecord) {
        // 如果存在，则更新记录
        this.logger.log(
          `更新诊断记录: ${createDiagnosticRecordDto.idNumber} - ${createDiagnosticRecordDto.diagnosisNumber}`,
        );

        // 更新时间
        createDiagnosticRecordDto.updatedAt = new Date();

        // 更新记录
        await this.diagnosticRecordRepository.update(
          { _id: existingRecord._id },
          {
            diagnosisConclusionDescription: createDiagnosticRecordDto.diagnosisConclusion,
            ...createDiagnosticRecordDto,
          },
        );

        // 返回更新后的记录
        result = await this.diagnosticRecordRepository.findOne({
          where: { _id: existingRecord._id },
        });
      } else {
        // 如果不存在，则创建新记录
        this.logger.log(
          `创建诊断记录: ${createDiagnosticRecordDto.idNumber} - ${createDiagnosticRecordDto.diagnosisNumber}`,
        );

        // 设置创建和更新时间
        createDiagnosticRecordDto.createdAt = new Date();
        createDiagnosticRecordDto.updatedAt = new Date();

        // 创建并保存新记录
        const newRecord = this.diagnosticRecordRepository.create(
          {
            diagnosisConclusionDescription: createDiagnosticRecordDto.diagnosisConclusion,
            ...createDiagnosticRecordDto,
          },
        );
        result = await this.diagnosticRecordRepository.save(newRecord);
      }

      // 根据身份证号更新users表中的职业病相关字段
      await this.updateUserOccupationalDiseaseFromDiagnostic(
        createDiagnosticRecordDto.idNumber,
        createDiagnosticRecordDto.hasOccupationalDisease,
        createDiagnosticRecordDto.diagnosisConclusionDescription || createDiagnosticRecordDto.diagnosisConclusion,
        createDiagnosticRecordDto.occupationalDisease,
        createDiagnosticRecordDto.diagnosisNumber
      );

      return result;
    } catch (error) {
      this.logger.error(
        `创建或更新诊断记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 创建或更新鉴定记录
   * @param createDeterminationRecordDto 鉴定记录DTO
   * @returns 创建或更新的鉴定记录
   */
  async createOrUpdateDeterminationRecord(
    createDeterminationRecordDto: CreateDeterminationRecordDto,
  ): Promise<DeterminationRecord> {
    try {
      // 查询是否存在相同身份证和鉴定编号的记录
      const existingRecord = await this.determinationRecordRepository.findOne({
        where: {
          idNumber: createDeterminationRecordDto.idNumber,
          determinationNumber: createDeterminationRecordDto.determinationNumber,
        },
      });

      // 兼容 entity 字段的默认值
      if (!createDeterminationRecordDto.occupationalDisease) {
        createDeterminationRecordDto.occupationalDisease = [];
      }
      if (!createDeterminationRecordDto.fileList) {
        createDeterminationRecordDto.fileList = [];
      }
      if (!createDeterminationRecordDto.createdAt) {
        createDeterminationRecordDto.createdAt = new Date();
      }
      if (!createDeterminationRecordDto.updatedAt) {
        createDeterminationRecordDto.updatedAt = new Date();
      }

      if (existingRecord) {
        // 如果存在，则更新记录
        this.logger.log(
          `更新鉴定记录: ${createDeterminationRecordDto.idNumber} - ${createDeterminationRecordDto.determinationNumber}`,
        );

        // 更新时间
        createDeterminationRecordDto.updatedAt = new Date();

        // 更新记录
        await this.determinationRecordRepository.update(
          { _id: existingRecord._id },
          {
            // 只在 determinationConclusionDescription 为空时用 determinationConclusion
            determinationConclusionDescription: createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
            ...createDeterminationRecordDto
          },
        );

        // 返回更新后的记录
        const result = await this.determinationRecordRepository.findOne({
          where: { _id: existingRecord._id },
        });

        // 根据身份证号更新users表中的职业病相关字段
        await this.updateUserOccupationalDiseaseFromDetermination(
          createDeterminationRecordDto.idNumber,
          createDeterminationRecordDto.hasOccupationalDisease,
          createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
          createDeterminationRecordDto.occupationalDisease,
          createDeterminationRecordDto.diagnosisNumber,
          createDeterminationRecordDto.determinationNumber,
          createDeterminationRecordDto.firstDeterminationNumber
        );

        return result;
      } else {
        // 如果不存在，则创建新记录
        this.logger.log(
          `创建鉴定记录: ${createDeterminationRecordDto.idNumber} - ${createDeterminationRecordDto.determinationNumber}`,
        );

        // 设置创建和更新时间
        createDeterminationRecordDto.createdAt = new Date();
        createDeterminationRecordDto.updatedAt = new Date();

        // 创建并保存新记录
        const newRecord = this.determinationRecordRepository.create(
          {
            determinationConclusionDescription: createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
            ...createDeterminationRecordDto
          },
        );
        const result = await this.determinationRecordRepository.save(newRecord);

        // 根据身份证号更新users表中的职业病相关字段
        await this.updateUserOccupationalDiseaseFromDetermination(
          createDeterminationRecordDto.idNumber,
          createDeterminationRecordDto.hasOccupationalDisease,
          createDeterminationRecordDto.determinationConclusionDescription ?? createDeterminationRecordDto.determinationConclusion,
          createDeterminationRecordDto.occupationalDisease,
          createDeterminationRecordDto.diagnosisNumber,
          createDeterminationRecordDto.determinationNumber,
          createDeterminationRecordDto.firstDeterminationNumber
        );

        return result;
      }
    } catch (error) {
      this.logger.error(
        `创建或更新鉴定记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号查询诊断记录
   * @param idNumber 身份证号
   * @returns 诊断记录列表
   */
  async findDiagnosticRecordsByIdNumber(idNumber: string): Promise<DiagnosticRecord[]> {
    try {
      return await this.diagnosticRecordRepository.find({
        where: { idNumber },
      });
    } catch (error) {
      this.logger.error(
        `查询诊断记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号查询鉴定记录
   * @param idNumber 身份证号
   * @returns 鉴定记录列表
   */
  async findDeterminationRecordsByIdNumber(idNumber: string): Promise<DeterminationRecord[]> {
    try {
      return await this.determinationRecordRepository.find({
        where: { idNumber },
      });
    } catch (error) {
      this.logger.error(
        `查询鉴定记录失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据诊断记录更新用户的职业病相关信息
   * @param idNumber 身份证号
   * @param hasOccupationalDisease 是否患有职业病
   * @param diseaseDescription 职业病描述
   * @param occupationalDisease 职业病数组
   * @param diagnosisNumber 诊断编号（用于日志记录）
   */
  private async updateUserOccupationalDiseaseFromDiagnostic(
    idNumber: string,
    hasOccupationalDisease: boolean,
    diseaseDescription: string,
    occupationalDisease: { name: string; code: string }[],
    diagnosisNumber: string
  ): Promise<void> {
    try {
      // 查找用户
      const user = await this.usersRepository.findOne({
        where: { idNo: idNumber },
      });

      if (!user) {
        this.logger.warn(`未找到用户: ${idNumber}`);
        return;
      }

      // 如果诊断结果为非职业病，不更新用户信息
      if (!hasOccupationalDisease) {
        this.logger.log(`诊断结果为非职业病，不更新用户信息: ${idNumber}`);
        return;
      }

      // 获取用户当前的职业病列表
      let userOccupationalDiseases = user.occupationalDisease || [];

      // 将新的职业病添加到列表中
      if (occupationalDisease && occupationalDisease.length > 0) {
        // 合并职业病列表
        const combinedDiseases = [...userOccupationalDiseases, ...occupationalDisease];

        // 去重 (基于name和code)
        userOccupationalDiseases = combinedDiseases.filter((disease, index, self) =>
          index === self.findIndex(d => d.name === disease.name && d.code === disease.code)
        );
      }

      // 更新用户信息
      this.logger.log(`更新用户职业病信息(诊断 ${diagnosisNumber}): ${idNumber}`);
      await this.usersRepository.update(
        { _id: user._id },
        {
          hasOccupationalDisease: true, // 如果有职业病，设置为true
          occupationalDiseaseDescription: diseaseDescription || '',
          occupationalDisease: userOccupationalDiseases,
          updateTime: new Date(),
        }
      );

    } catch (error) {
      this.logger.error(
        `更新用户职业病信息(诊断)失败: ${error.message}`,
        error.stack,
      );
      // 不抛出异常，以免影响主流程
    }
  }

  /**
   * 根据鉴定记录更新用户的职业病相关信息
   * @param idNumber 身份证号
   * @param hasOccupationalDisease 是否患有职业病
   * @param diseaseDescription 职业病描述
   * @param occupationalDisease 职业病数组
   * @param diagnosisNumber 诊断编号
   * @param determinationNumber 鉴定编号
   * @param firstDeterminationNumber 首次鉴定编号
   */
  private async updateUserOccupationalDiseaseFromDetermination(
    idNumber: string,
    hasOccupationalDisease: boolean,
    diseaseDescription: string,
    occupationalDisease: { name: string; code: string }[],
    diagnosisNumber: string,
    determinationNumber: string,
    firstDeterminationNumber?: string
  ): Promise<void> {
    try {
      // 查找用户
      const user = await this.usersRepository.findOne({
        where: { idNo: idNumber },
      });

      if (!user) {
        this.logger.warn(`未找到用户: ${idNumber}`);
        return;
      }

      // 获取用户当前的职业病列表
      let userOccupationalDiseases = user.occupationalDisease || [];

      // 查找对应的诊断记录
      const diagnosticRecord = await this.diagnosticRecordRepository.findOne({
        where: { diagnosisNumber: diagnosisNumber }
      });

      if (!diagnosticRecord) {
        this.logger.warn(`未找到对应的诊断记录: ${diagnosisNumber}`);
      } else {
        // 如果找到了对应的诊断记录，需要从用户的职业病列表中移除该诊断记录中的职业病
        if (diagnosticRecord.occupationalDisease && diagnosticRecord.occupationalDisease.length > 0) {
          // 过滤掉诊断记录中的职业病
          userOccupationalDiseases = userOccupationalDiseases.filter(disease => {
            return !diagnosticRecord.occupationalDisease.some(d =>
              d.name === disease.name && d.code === disease.code
            );
          });
        }
      }

      // 如果是再鉴定，还需要查找并处理首次鉴定的记录
      if (firstDeterminationNumber) {
        const firstDeterminationRecord = await this.determinationRecordRepository.findOne({
          where: { determinationNumber: firstDeterminationNumber }
        });

        if (firstDeterminationRecord) {
          // 如果找到了首次鉴定记录，需要从用户的职业病列表中移除该鉴定记录中的职业病
          if (firstDeterminationRecord.occupationalDisease && firstDeterminationRecord.occupationalDisease.length > 0) {
            // 过滤掉首次鉴定记录中的职业病
            userOccupationalDiseases = userOccupationalDiseases.filter(disease => {
              return !firstDeterminationRecord.occupationalDisease.some(d =>
                d.name === disease.name && d.code === disease.code
              );
            });
          }
        }
      }

      // 将新的职业病添加到列表中
      if (hasOccupationalDisease && occupationalDisease && occupationalDisease.length > 0) {
        // 合并职业病列表
        const combinedDiseases = [...userOccupationalDiseases, ...occupationalDisease];

        // 去重 (基于name和code)
        userOccupationalDiseases = combinedDiseases.filter((disease, index, self) =>
          index === self.findIndex(d => d.name === disease.name && d.code === disease.code)
        );
      }

      // 检查用户是否还有职业病
      const stillHasOccupationalDisease = userOccupationalDiseases.length > 0;

      // 更新用户信息
      this.logger.log(`更新用户职业病信息(鉴定 ${determinationNumber}): ${idNumber}`);
      await this.usersRepository.update(
        { _id: user._id },
        {
          hasOccupationalDisease: stillHasOccupationalDisease,
          occupationalDiseaseDescription: stillHasOccupationalDisease ? (diseaseDescription || '') : '',
          occupationalDisease: userOccupationalDiseases,
          updateTime: new Date(),
        }
      );

    } catch (error) {
      this.logger.error(
        `更新用户职业病信息(鉴定)失败: ${error.message}`,
        error.stack,
      );
      // 不抛出异常，以免影响主流程
    }
  }

  async updateDiagnosisStatus(
    checkNo: string,
    status: string,
  ): Promise<string> {
    try {
      const healthCheck = await this.healthCheckRegisterRepository.findOne({
        where: { checkNo },
      });

      if (!healthCheck) {
        this.logger.warn(`未找到体检记录: ${checkNo}`);
        return null;
      }

      await this.healthCheckRegisterRepository.update(
        { _id: healthCheck._id },
        { diagnosisStatus: status as any },
      );

      return '更新体检记录成功';
    } catch (error) {
      this.logger.error(
        `更新体检记录(诊断状态)失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // #region 电子档案抽取
  /**
   * 根据身份证号抽取劳动者职业健康监护档案
   * @param idNumber 身份证号
   * @returns 创建的职业健康监护档案
   */
  async extractHealthArchive(idNumber: string): Promise<HealthSurvArchives> {
    try {
      // 1. 一般概况
      let basicData = <any>{
        _id: shortid.generate(),
        idNumber,
      }
      let user = await this.usersRepository.findOne({ where: { idNo: idNumber } });
      if (!user) {
        throw new Error(`未找到身份证号为 ${idNumber} 的用户`);
      }
      const employee = await this.employeesRepository.findOne({
        where: { _id: user.employeeId },
      });

      const birthDate = idNumber.substring(6, 14);
      const age = moment().diff(birthDate, 'years');

      const { name, gender, phoneNum } = user
      const { hobby, marriage, nativePlace } = employee;

      basicData = {
        ...basicData,
        name,
        gender,
        age,
        phoneNum,
        hobby,
        marriage,
        nativePlace,
        EnterpriseID: user.companyId[user.companyId.length - 1],
        employeeId: user.employeeId,
        userId: user._id,
      }

      // 2. 职业史
      let workHistory = []
      // 3. 接触史
      let exposureHistory = []

      const occupationalHistories = await this.occupationalHistoryRepository.find({
        where: { employeeId: user.employeeId },
      });

      occupationalHistories && occupationalHistories.forEach(oh => {
        const temp = {
          entryTime: oh.entryTime,
          leaveTime: oh.leaveTime,
          workUnit: oh.workUnit,
          workshop: oh.workshop,
          station: oh.station,
          workType: oh.workType,
        }
        workHistory.push(temp)
        exposureHistory.push({ ...temp, harmFactors: oh.harmFactors })
      })

      // 4. 既往史
      let pastHistory = []

      const employeeBasicInfo = await this.employeeBasicInfoRepository.findOne({
        where: { IDNum: idNumber },
      });

      employeeBasicInfo && employeeBasicInfo.pastHistory.forEach(ph => {
        pastHistory.push({
          diseaseName: ph.diseaseName,
          diagnosisDate: ph.diagnosisDate,
          institutionName: ph.institutionName,
          treatmentProcess: ph.treatmentProcess,
          outcomeCode: ph.outcomeCode,
        })
      })

      // 5. 作业场所危害因素检测结果
      let workspaceHarmResult = []
      workspaceHarmResult = await this.getCheckResultWorkspace({
        EnterpriseID: basicData.EnterpriseID,
        employeeId: basicData.employeeId,
      })

      // 6. 职业健康检查结果及处理情况
      let healthCheckResult = []

      const healthCheckRegisters = await this.healthCheckRegisterRepository.aggregate([
        { $match: { idNumber, status: 3 } },
        {
          $lookup: {
            from: 'physicalExamOrgs',
            localField: 'physicalOrgID',
            foreignField: '_id',
            as: 'physicalOrg'
          }
        },
        { $addFields: { physicalOrgName: { $arrayElemAt: ['$physicalOrg.name', 0] } } },
        { $project: { physicalOrg: 0, } },
      ]).toArray();
      healthCheckRegisters && healthCheckRegisters.forEach(hcr => {
        healthCheckResult.push({
          checkNo: hcr.checkNo,
          reportTime: hcr.reportTime || hcr.registerTime,
          checkConclusion: hcr.jobConclusion,
          physicalOrgName: hcr.physicalOrgName
        })
      })

      const healthSurvArchives = {
        ...basicData,
        pastHistory,
        workHistory,
        exposureHistory,
        workspaceHarmResult,
        healthCheckResult,
      }

      const res = await this.healthSurvArchivesRepository.save(healthSurvArchives);
      return res;

    }
    catch (error) {
      this.logger.error(
        `创建职业健康监护档案失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号抽取劳动者职业病诊断档案
   * @param idNumber 身份证号
   * @returns 创建的职业病诊断档案
   */
  async extractDiagnosticArchive(idNumber: string): Promise<DiagnosticArchives> {
    try {
      // 1. 根据身份证号获取基本信息
      const user = await this.usersRepository.findOne({ where: { idNo: idNumber } });
      if (!user) {
        throw new Error(`未找到身份证号为 ${idNumber} 的用户`);
      }

      // 计算年龄
      const birthDate = idNumber.substring(6, 14);
      const age = moment().diff(birthDate, 'years');

      // 2. 根据身份证号获取所有诊断记录，按诊断日期排序
      const diagnosticRecords = await this.diagnosticRecordRepository.find({
        where: { idNumber },
        order: { diagnosisDate: 'ASC' } // 按诊断日期升序排序
      });

      if (!diagnosticRecords || diagnosticRecords.length === 0) {
        throw new Error(`未找到身份证号为 ${idNumber} 的诊断记录`);
      }

      // 3. 汇总数据并保存
      const diagnosticArchive = {
        _id: shortid.generate(),
        idNumber,
        name: user.name,
        age,
        gender: user.gender,
        diagnosticRecord: diagnosticRecords,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = await this.diagnosticArchivesRepository.save(diagnosticArchive);
      this.logger.log(`成功创建职业病诊断档案: ${idNumber}, 包含 ${diagnosticRecords.length} 条诊断记录`);

      return result;
    } catch (error) {
      this.logger.error(
        `创建职业病诊断档案失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 根据身份证号抽取劳动者职业病鉴定档案
   * @param idNumber 身份证号
   * @returns 创建的职业病鉴定档案
   */
  async extractDeterminationArchive(idNumber: string): Promise<DeterminationArchives> {
    try {
      // 1. 根据身份证号获取基本信息
      const user = await this.usersRepository.findOne({ where: { idNo: idNumber } });
      if (!user) {
        throw new Error(`未找到身份证号为 ${idNumber} 的用户`);
      }

      // 计算年龄
      const birthDate = idNumber.substring(6, 14);
      const age = moment().diff(birthDate, 'years');

      // 2. 根据身份证号获取所有鉴定记录，按鉴定日期排序
      const determinationRecords = await this.determinationRecordRepository.find({
        where: { idNumber },
        order: { determinationDate: 'ASC' } // 按鉴定日期升序排序
      });

      if (!determinationRecords || determinationRecords.length === 0) {
        throw new Error(`未找到身份证号为 ${idNumber} 的鉴定记录`);
      }

      // 3. 汇总数据并保存
      const determinationArchive = {
        _id: shortid.generate(),
        idNumber,
        name: user.name,
        age,
        gender: user.gender,
        determinationRecord: determinationRecords,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      const result = await this.determinationArchivesRepository.save(determinationArchive);
      this.logger.log(`成功创建职业病鉴定档案: ${idNumber}, 包含 ${determinationRecords.length} 条鉴定记录`);

      return result;
    } catch (error) {
      this.logger.error(
        `创建职业病鉴定档案失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  // #endregion

  // #region tool 

  /**
   * 获取作业场所危害因素检测结果
   * @param params EnterpriseID 企业id 
   * @param employeeId 员工id
   * @returns 
   */
  async getCheckResultWorkspace(params: { EnterpriseID: string, employeeId: string }): Promise<any[]> {
    try {
      const checkResults: Record<string, any> = {};

      // 获取工种
      const EnterpriseID = params.EnterpriseID;
      const res = await this.workspaceRepository.find({
        where: {
          'employees.employeeId': params.employeeId,
          EnterpriseID,
        },
      });

      const workTypes: any[] = [];
      res.forEach(item => {
        const { workshopName, workspaceName, workTypeName } = item as any;
        let harmFactors: string[] = [];
        item.stations.forEach(station => {
          harmFactors.push(...station.harmFactors);
        });
        harmFactors = Array.from(new Set(harmFactors));
        workTypes.push({ workshopName, workspaceName, workTypeName, harmFactors });
      });

      // 获取所有危害因素对应的类别
      const AllHarm = await this.findAllHarmFactors();
      const AllHarmCategory: { harm: string; category: string }[] = [];
      AllHarm.forEach((category: any) => {
        category.harmFactors.forEach((harm: any) => {
          AllHarmCategory.push({ harm: harm.label, category: category.label });
        });
      });

      for (const workspace of workTypes) {
        const { workTypeName, workspaceName, workshopName, harmFactors } = workspace;
        // 对当前所在工作场所的所有点位的所有危害因素进行处理(获取category)
        const harmFactorsAndSort = harmFactors.map(harm => {
          const foundItem = AllHarmCategory.find(item => item.harm === harm);
          const category = foundItem ? foundItem.category : null;
          return [category, harm];
        });

        for (const harm of harmFactorsAndSort) {
          let model = '';
          let modelName = '';
          const checkProjectFields: any = {};

          if (harm[0] === '化学') {
            model = 'chemistryFactors';
            modelName = '化学';
            checkProjectFields[`${model}.formData.checkProject`] = harm[1].trim();
          } else if (harm[0] === '粉尘') {
            model = 'dustFactors';
            modelName = '粉尘';
            checkProjectFields[`${model}.formData.checkProject`] = { $regex: harm[1].trim() };
          } else if (harm[0] === '生物') {
            model = 'biologicalFactors';
            modelName = '生物';
            checkProjectFields[`${model}.formData.checkProject`] = harm[1].trim();
          } else {
            if (harm[1].includes('噪声')) {
              model = 'noiseFactors';
              modelName = '噪声';
            } else if (harm[1].includes('高温')) {
              model = 'heatFactors';
              modelName = '高温';
            } else if (harm[1].includes('超高频辐射')) {
              model = 'ultraHighRadiationFactors';
              modelName = '超高频辐射';
            } else if (harm[1].includes('高频电磁场')) {
              model = 'highFrequencyEleFactors';
              modelName = '高频电磁场';
            } else if (harm[1].includes('工频电磁场')) {
              model = 'powerFrequencyElectric';
              modelName = '工频电场';
            } else if (harm[1].includes('激光')) {
              model = 'laserFactors';
              modelName = '激光辐射';
            } else if (harm[1].includes('微波')) {
              model = 'microwaveFactors';
              modelName = '微波辐射';
            } else if (harm[1].includes('紫外线')) {
              model = 'ultravioletFactors';
              modelName = '紫外线';
            } else if (harm[1].includes('振动')) {
              model = 'handBorneVibrationFactors';
              modelName = '手传振动';
            } else if (harm[1].includes('游离二氧化硅')) {
              model = 'SiO2Factors';
              modelName = '游离二氧化硅';
            }
          }
          if (model) {
            // $match
            const match: any = {};
            match[`${model}.formData.mill`] = { $regex: workshopName };
            match[`${model}.formData.workspace`] = { $regex: workspaceName };
            match[`${model}.formData.workType`] = { $regex: workTypeName };

            // $project
            const project: any = {};
            project[model] = 1;
            project.jobHealthId = 1;
            project['jobHealth.name'] = 1;
            project['jobHealth.reportTime'] = 1;

            const pipeline = [
              { $match: { EnterpriseID } },
              {
                $lookup: {
                  from: 'jobhealths',
                  localField: 'jobHealthId',
                  foreignField: '_id',
                  as: 'jobHealth',
                },
              },
              { $unwind: '$jobHealth' },
              { $unwind: `$${model}.formData` },
              { $match: { ...checkProjectFields, ...match } },
              { $project: project },
            ];

            const checkResultItem = await this.checkAssessmentRepository.aggregate(pipeline).toArray() as any[];

            for (const item of checkResultItem) {
              if (item && item.jobHealth && item[model]?.formData) {
                item[model].formData.checkTime = item.jobHealth.reportTime
                  ? moment(new Date(item.jobHealth.reportTime)).format('YYYY-MM-DD')
                  : '';
                item[model].formData.checkName = item.jobHealth.name;
                item[model].formData.checkProject = item[model].formData.checkProject || modelName;
                item[model].formData.checkResult = item[model].formData.checkResult || item[model].formData.conclusion;
                item[model].formData.protectiveFacilities = workspace.protectiveFacilities;
                if (!checkResults[model]) {
                  checkResults[model] = { data: [] };
                }
                checkResults[model].model = model;
                checkResults[model].modelName = modelName;
                checkResults[model].data.push(item[model].formData);
              }
            }
          }
        }
      }
      return Object.values(checkResults);
    }
    catch (error) {
      this.logger.error(
        `获取作业场所危害因素检测结果失败: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 获取所有危害因素
   * @returns 
   */
  async findAllHarmFactors(): Promise<OccupationalExposureLimits[]> {
    try {
      const res = await this.occupationalExposureLimitsRepository.aggregate([
        { $unwind: '$chineseName' },
        { $group: { _id: '$catetory', harmFactors: { $push: { label: '$chineseName' } } } },
        { $project: { label: '$_id', harmFactors: 1 } },
      ]).toArray();
      return res;
    } catch (error) {
      this.logger.error(
        `获取所有危害因素失败: ${error.message}`,
        error.stack,
      );
      return [];
    }
  }

  // #endregion
}