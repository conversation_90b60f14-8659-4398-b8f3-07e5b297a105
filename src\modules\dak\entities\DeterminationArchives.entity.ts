import { <PERSON><PERSON>ty, Column, ObjectId<PERSON><PERSON>umn, CreateDateColumn, UpdateDateColumn, BeforeInsert } from 'typeorm';
import * as shortid from 'shortid';
import { DeterminationRecord } from './determinationRecord.entity';

// 职业病鉴定档案
@Entity('determinationArchives')
export class DeterminationArchives {
  @ObjectIdColumn()
  _id: string;

  @Column({ comment: '身份证号' })
  idNumber: string;

  @Column({ comment: '姓名' })
  name: string;

  @Column({ comment: '年龄' })
  age: number;

  @Column({ comment: '性别' })
  gender: string;

  @Column({ comment: '鉴定记录' })
  determinationRecord: DeterminationRecord[] = [];

  @CreateDateColumn({ comment: '创建时间' })
  createdAt: Date;

  @UpdateDateColumn({ comment: '更新时间' })
  updatedAt: Date;

  @BeforeInsert()
  setDefaults() {
    if (!this._id) {
      this._id = shortid.generate();
    }
    const now = new Date();
    this.createdAt = now;
    this.updatedAt = now;
  }
}